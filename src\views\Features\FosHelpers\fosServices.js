import { CALL_API } from "../../../services";
import { localStorageCache } from "../../../utils/utility";

export const IsSourceCustomerWhatsapp = () => {
    try {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        let src = params.get("src");
        if (!src) {
            return false;
        }
        return (['customerwhatsapp', 'customersms'].indexOf(src.toLowerCase())) > -1 ? true : false
    }
    catch {
        return false;
    }


}

export const GetAvailableSlotsService = (parentLeadId, AssignmentTypeId = 0) => {

    //var reqData={};
    if (IsSourceCustomerWhatsapp()) {
        parentLeadId = 0;
        AssignmentTypeId = 0;
    }
    else if (!AssignmentTypeId) {
        console.error("AssignmentId can not be 0");
    }
    const input = {
        url: `fos/api/fos/getAppointmentSlots/${parentLeadId}?AssignmentTypeId=${AssignmentTypeId}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 6000
    }
    return CALL_API(input).then(function (response) {
        return response;
    });
}
export const GetOfflineCitiesService = (parentLeadId, Type, productId) => {

    let cachedData = localStorageCache.readFromCache(`OfflineCities${productId}_${Type}`);

    if (cachedData !== null) { return Promise.resolve(cachedData) }
    if (IsSourceCustomerWhatsapp()) {
        parentLeadId = 0;
    }


    const input = { 
        url: `api/SalesView/GetOfflineCities/${parentLeadId}/${Type}/${productId}`,
        method: 'GET', service: 'MatrixCoreAPI'
    };
    return CALL_API(input).then(function (response) {
        if (Array.isArray(response) && response.length > 0) {
            localStorageCache.writeToCache(`OfflineCities${productId}_${Type}`, response);
        }
        return response;
    })
}

export const SetAppointmentDataService = (reqData) => {
    const _input = {
        // url: `api/SalesView/SetAppointmentData`,
        url: `api/FOS/SetAppointmentDataV2`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: reqData,
        timeout: "l"
    }
    return CALL_API(_input)
}

export const GetPincodesForCityIDService = (cityId,ProductId,LeadSource) => {
    const input = {
        url: `fos/api/fos/getPincodeByCityId/${cityId}?ProductId=${ProductId}&LeadSource=${LeadSource}`,
        method: "GET",
        service: "MatrixCoreAPI"
    }
    return CALL_API(input).then(function (response) {
        if (Array.isArray(response)) {
            return response;
        }
        return [];
    }).catch((err) => {

        console.error(err);
        return [];
    });

}

export const GetAgentTypeByBookingService = (userId, BookingId = 0) => {
    const input = {
        // url: `api/BMS/GetAgentTypeByBooking/${userId}/${BookingId}`,
        url: `api/SalesView/GetAgentTypeByAgentId/${userId}`,
        method: "GET",
        service: "MatrixCoreAPI"
    }
    return CALL_API(input);
}
export const getLandmarkdetailsService = (reqData) => {
    const _input = {
        url: `fos/api/FOS/getAutoSuggestions`,
        method: 'POST', service: 'MatrixCoreAPI',// Need to change internal
        requestData: reqData,
        timeout: "l"
    }
    return CALL_API(_input)
}

export const GetAppointmentDataService = (CustomerId, parentLeadId) => {
    var reqData = {};
    if (IsSourceCustomerWhatsapp() == true) {
        reqData = {
            "EncryptedLeadId": parentLeadId,
            "EncryptedCustomerId": CustomerId
        }

    }
    else {
        reqData = {
            "ParentId": parentLeadId,
            "CustomerId": CustomerId
        }
    }
    const input = {
        // url: `api/SalesView/GetAppointmentData/${CustomerId}/${parentLeadId}`,
        url: `api/SalesView/GetAppointmentData`,
        method: 'POST', service: 'MatrixCoreAPI', requestData: reqData, timeout: 6000//timeout: "l"
    };
    return CALL_API(input)

}
export const UpdateAppointmentStatusService = (requestData) => {
    const input = {
        url: `api/FOS/UpdateAppointmentStatus`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: requestData
    };
    return CALL_API(input);
}

export const CancellationReasonsService = (productId, subStatusID) => {
    const input = {
        url: `api/SalesView/GetReasonMaster/${productId}/${subStatusID}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

export const SaveAppCancelReasonService = (requestData) => {
    const input = {
        url: `api/SalesView/SaveAppCancelReason`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData
    };
    return CALL_API(input);
}

export const getAppointmentsBySlotIdService = (requestData) => {
    const input = {
        url: `fos/api/fos/getAppointmentsBySlotId`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData
    };
    return CALL_API(input);
}

export const getBasicLeadDetailsService = (LeadId) => {

    const input = {
        url: `api/WebSiteService/GetBasicLeadDetails`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: { LeadId }
    };
    return CALL_API(input);
}

export const getIsLeadRenewalService = (parentLeadId) => {
    const input = {
        url: `coremrs/api/LeadDetails/CheckIsLeadSetRenewal/${parentLeadId}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}
export const GetIsAppointmentEligiblePostChurn = (parentLeadId) => {
    const input = {
        url: `fos/api/fos/IsAppointmentEligible/${parentLeadId}`,
        method: "GET",
        service: "MatrixCoreAPI"
    }
    return CALL_API(input);
}

export const IsAppMarkCancelService = (parentLeadId) => {
    const input = {
        url: `api/SalesView/IsAppMarkCancel/${parentLeadId}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

export const GetLatLongByPlaceId = (PlaceId) => {
    const input = {
        url: `api/fos/GetLatLongByPlaceId/${PlaceId}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

export const AskCustomerLocation=(requestData)=>
{
    const input=
    {
        url:`api/fos/SaveCustomerLocationData`,
        method:'POST',service:'MatrixCoreAPI',
        requestData
    };
    return CALL_API(input);
}

export const TriggerSaveCustomerLocationData=(requestData)=>
{
    const input=
    {
        url:`api/fos/TriggerSaveCustomerLocationData`,
        method:'POST',service:'MatrixCoreAPI',
        requestData
    };
    return CALL_API(input);
}

export const GetCustomerLocationData=(CustomerId, parentLeadId)=>
{
    var requestData = {};
    if (IsSourceCustomerWhatsapp() == true) {
        requestData = {
            "EncryptedLeadId": parentLeadId,
            "EncryptedCustomerId": CustomerId
        }

    }
    else {
        requestData = {
            "ParentId": parentLeadId,
            "CustomerId": CustomerId
        }
    }
    const input=
    {
        url:`api/fos/GetCustomerLocationData`,
        method:'POST', service:'MatrixCoreAPI',
        requestData
    }
    return CALL_API(input);
}

export const CheckCustomerLocationAvailable = (parentLeadId) => {
    const input = {
        url: `api/fos/CheckCustomerLocationAvailable/${parentLeadId}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

export const IsAppointmentCreatedService = (parentLeadId) => {
  
    var requestData = {};
    if (IsSourceCustomerWhatsapp() == true) {
        requestData = {
            "EncryptedLeadId": parentLeadId,
        }
    }
    else {
        requestData = {
            "LeadId": parentLeadId,
        }
    }
    const input = {
        url: `api/fos/IsAppointmentCreated`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData
    };
    return CALL_API(input);
  
}

export const CheckAgentAvailabilityInCity=(parentLeadId,CityId,SlotId,AppointmentDateTime,UserId)=>
{
    const input=
    {
        url:`api/fos/CheckAgentAvailabilityInCity/${parentLeadId}/${CityId}/${SlotId}/${AppointmentDateTime}/${UserId}`,
        method:'GET',service:'MatrixCoreAPI',
    };
    return CALL_API(input);

}

export const SaveCoreAddressUsageService = (reqData) => {
    const _input = {
        // url: `api/SalesView/SetAppointmentData`,
        url: `api/fos/SaveCoreAddressUsage`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: reqData,
        timeout: 3000
    }
    return CALL_API(_input)
}

export const GetTotalAppointmentsByCityIdService = (CityId) => {
    const _input = {
        // url: `api/SalesView/SetAppointmentData`,
        url: `api/fos/GetTotalAppointmentsByCityId/${CityId}`,
        method: 'GET', service: 'MatrixCoreAPI',
       
    }
    return CALL_API(_input)
}

export const GetCarDetailsService = (LeadId) => {
    const _input = {
        // url: `api/SalesView/SetAppointmentData`,
        url: `api/fos/GetCarDetails/${LeadId}`,
        method: 'GET', service: 'MatrixCoreAPI',
       
    }
    return CALL_API(_input)
}

export const IsActiveAppointmentService = (LeadId) => {
    const _input = {
        // url: `api/SalesView/SetAppointmentData`,
        url: `api/fos/IsActiveAppointment/${LeadId}`,
        method: 'GET', service: 'MatrixCoreAPI',
       
    }
    return CALL_API(_input)
}

export const getBasicLeadDetailsWrapperService = (LeadId) => {

    const input = {
        url: `api/WebSiteService/GetBasicLeadDetailsWrapper`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: { LeadId }
    };
    return CALL_API(input);
}


export const getPrefCommService = (requestData) => {

    const _input = {
        url: `api/SalesView/GetPrefComm`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: requestData
    }
    return CALL_API(_input)

}

export const requestWhatsAppOptIn = (requestBody) => {
    const input = {
        url: `api/SalesView/SendOptinLinktoCustomer`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: requestBody
    };
    return CALL_API(input);
}
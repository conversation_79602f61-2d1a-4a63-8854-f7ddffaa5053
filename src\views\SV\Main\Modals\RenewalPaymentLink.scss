// Variables
$primary-blue: #0065ff;
$primary-blue-dark: #0052cc;
$primary-blue-light: #e3f2fd;
$primary-blue-lighter: #f8f9ff;
$text-primary: #2e2e2e;
$text-secondary: #808080;
$text-error: #df1f1f;
$border-radius: 8px;
$border-radius-large: 16px;
$shadow-light: 0px 0px 16px #00000014;
$transition-smooth: all 0.3s ease;

// Mixins
@mixin button-base {
  border-radius: $border-radius;
  cursor: pointer;
  outline: none;
  font-family: Roboto;
  font-weight: 600;
  transition: $transition-smooth;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

@mixin roboto-font($size: 12px, $weight: normal, $line-height: 16px) {
  font: normal normal #{$weight} #{$size}/#{$line-height} Roboto;
}

.RenewalLinkPopup {
  .MuiDialog-paperWidthSm {
    width: 600px;
    height: 83%;
    padding-bottom: 0;
    box-shadow: $shadow-light;
    border-radius: $border-radius-large;

    .MuiDialogContent-root {
      padding: 0 8px !important;
      height: auto;

      // Tabs Styling
      .MuiTabs-root {
        padding: 0 25px;
      }

      .MuiTab-root {
        min-width: auto;
        margin-right: 35px;
        text-transform: capitalize;
        @include roboto-font(14px, 600, 19px);
        color: $text-primary;

        &.Mui-selected {
          opacity: 1;
          color: $primary-blue;
          border-bottom: 3px solid $primary-blue;
          border-radius: 2px;
        }
      }

      .MuiTabs-indicator {
        display: none;
      }

      // Table Styling
      .MuiTableCell-root {
        @include roboto-font(12px, normal, 16px);
        padding: 10px 5px;
        color: $text-secondary;
        border-bottom: none;
        text-align: center;
      }

      .MuiTableHead-root {
        background-color: transparent;
      }

      .MuiTableRow-root td {
        padding: 5px;
        @include roboto-font(12px, 500, 16px);
        color: $text-primary;
        border: none;
        text-align: center;
      }

      .MuiPaper-elevation1 {
        box-shadow: none;
      }

      // Typography
      h6 {
        text-align: left;
        @include roboto-font(18px, 600, 24px);
        color: $text-primary;
        padding-left: 10px;
        letter-spacing: 0;
      }
    }

    .RenewalLinkPopupHistory {

      .button-txt {
        background: white;
        border: 1px solid $primary-blue;
        border-radius: $border-radius;
        letter-spacing: 0.5px;
        padding: 11px 20px;
        width: 100%;
        min-width: 160px;
        color: $primary-blue;
        @include roboto-font(13px, 600, 20px);
        cursor: pointer;
        outline: none;
        margin: 0;

        &:hover {
          background: $primary-blue;
          color: white;
          box-shadow: 0 4px 15px rgba(0, 101, 255, 0.3);
        }
      }


      .button {
        background: white;
        border-radius: $border-radius;
        letter-spacing: 0.17px;
        padding: 5px;
        width: 100px;
        @include roboto-font(12px, 600, 21px);
        cursor: pointer;
        outline: none;
        color: $primary-blue;
        border-color: $primary-blue;
      }

      .pending-button-txt {
        background: white;
        border-radius: $border-radius;
        letter-spacing: 0.17px;
        padding: 5px;
        width: 100px;
        @include roboto-font(12px, 600, 21px);
        outline: none;
        margin: 0;
        color: $text-error;
        border-color: $text-error;
        cursor: pointer;
      }

      .disablebtn {
        cursor: default !important;
      }

      .TablePagination {
        .MuiTablePagination-input {
          margin-right: 8px !important;
        }

        .MuiButtonBase-root {
          position: static;
          background-color: transparent !important;
        }
      }

    }

    .SubmitBtn {
      @include button-base;
      background: $primary-blue-light;
      border: 1px solid #bbdefb;
      border-radius: $border-radius;
      letter-spacing: 0.3px;
      padding: 10px 20px;
      width: 150px;
      min-width: 140px;
      color: $primary-blue;
      @include roboto-font(14px, 600, 18px);
      margin-top: 6px;

      &:hover {
        background: #bbdefb;
        border-color: #90caf9;
        color: $primary-blue-dark;
      }
    }

    .custom-disabled-input input:disabled {
      background: #e5e5e5 !important;
      color: #6b6764 !important;
    }

    .CancellationAttechBtn {
      @include button-base;
      background: #FFF5F5;
      border: 1px solid #FFE0E0;
      border-radius: $border-radius;
      letter-spacing: 0.3px;
      padding: 12px 18px;
      width: 100%;
      min-width: 130px;
      @include roboto-font(14px, 500, 18px);
      color: #D63031;
      margin: 0;
      transition: $transition-smooth;

      &:hover {
        background: #FFEBEB;
        border-color: #FFB3B3;
        color: #B71C1C;
      }
    }

    .Note {
      color: $text-error;
      @include roboto-font(12px, 600, 15px);
      margin: 20px 0 0;
      text-align: center;
    }



    // Upload Section
    .upload-option {
      .upload-section {
        width: 100%;
      }

      .upload-label {
        @include roboto-font(13px, bold, 16px);
        letter-spacing: 0.22px;
        color: #253858;
        display: block;
        margin-bottom: 12px;
      }

      .upload-button-container {
        width: 100%;
        display: block;
      }

      .compact-upload-btn {
        @include button-base;
        width: 100%;
        min-height: 40px;
        padding: 7px 16px;
        @include roboto-font(13px, 500, 16px);
        border: 2px dashed $primary-blue;
        color: $primary-blue;
        background: $primary-blue-lighter;
        border-radius: $border-radius;
        text-transform: none;

        &:hover {
          border-color: $primary-blue-dark !important;
          background: $primary-blue-light !important;
          box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2) !important;
        }

        .upload-icon {
          font-size: 18px;
          position: relative;
          top: 5px;
        }
      }
    }
  }

  .hidden-file-input {
    display: none !important;
  }
  .MuiBox-root{
padding:25px 15px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {

  .upload-option {
    .upload-section {
      margin-top: 10px;
    }

    .compact-upload-btn {
      min-height: 40px !important;
      padding: 10px 12px !important;
      font-size: 12px !important;
    }

    .uploaded-file-container {
      padding: 10px 12px;
    }
  }

  .RenewalLinkPopup {
   .MuiTabs-root {
        padding:0px !important;
      }

    .MuiTab-root {
      margin-right: 0px !important;
    }

  }
}
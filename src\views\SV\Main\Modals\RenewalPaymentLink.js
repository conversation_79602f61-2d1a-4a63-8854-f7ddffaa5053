import { Grid, Button, Input, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useSnackbar } from "notistack";
import { SelectDropdown, TextInput } from '../../../../components';
import { useSelector } from "react-redux";
import { InsertRenewalPaymentDetails, GetRenewalDetails } from "../../../../services/Common";
import rootScopeService from "../../../../services/rootScopeService";
import MultipleSelectDropdown from "../../../../components/MultipleSelectDropdown";
import './RenewalPaymentLink.scss';
import masterService from "../../../../services/masterService";

// Constants
const MAX_FILE_SIZE = 1 * 1024 * 1024; // 1MB
const VALID_IMAGE_EXTENSIONS = ['png', 'jpg', 'jpeg'];
import FileUploadIcon from '@mui/icons-material/FileUpload';
let initialRenewalInfo = {
    InsurerName: '',
    PlanName: '',
    ProposerName: '',
    PolicyNo: '',
    PremiumAmount: 0,
    PaymentReason: '',
    SumInsured: 0,
    RiderDetails: '',
    SupplierId: 0,
    PlanID: 0
};

const Reasons = [
    { "Id": 1, "Name": "1 Year" }
    , { "Id": 2, "Name": "2 Year" }
    , { "Id": 3, "Name": "3 Year" }
    , { "Id": 4, "Name": "SI Increase" }
    , { "Id": 5, "Name": "SI Decrease" }
    , { "Id": 6, "Name": "Plan Change" }
    , { "Id": 7, "Name": "Member Addition" }
    , { "Id": 8, "Name": "Member Deletion" }
    , { "Id": 9, "Name": "Proposer Change" }
    , { "Id": 10, "Name": "Rider Addition" }
    , { "Id": 11, "Name": "Rider Removed" }
    , { "Id": 12, "Name": "DOB Correction" }
    , { "Id": 13, "Name": "CJ Not Available" }
    , { "Id": 14, "Name": "CJ Not Working" }
    , { "Id": 15, "Name": "Address Change" }
    , { "Id": 16, "Name": "Name Correction" }
    , { "Id": 17, "Name": "PED Addition" }
    , { "Id": 18, "Name": "Wellness Discount" }
    , { "Id": 19, "Name": "Member Split" }
    , { "Id": 20, "Name": "Split Payment" }
    , { "Id": 21, "Name": "Contact Change" }
    , { "Id": 22, "Name": "Policy Merge" }
    , { "Id": 23, "Name": "Website Discount" }
    , { "Id": 24, "Name": "Nominee Change" }
    , { "Id": 25, "Name": "Quarterly" }
    , { "Id": 26, "Name": "Monthly" }
    , { "Id": 27, "Name": "Half Yearly" }
    , { "Id": 28, "Name": "Easy EMI" }
    , { "Id": 29, "Name": "Others" }
];

const MultipleReasonsType = [
    ...Reasons
];

const RenewalPaymentLink = (props) => {
    const { enqueueSnackbar } = useSnackbar();
    const [RenewalLead, setRenewalLead] = useState(0);
    var allLeads = useSelector(state => state.salesview.allLeads);
    const [RenewDetails, setRenewDetails] = useState(undefined);
    const [RenewDetailsList, setRenewDetailsList] = useState(initialRenewalInfo);
    const [visibleLeads, setvisibleLeads] = useState([]);
    const [ReasonVisible, setReasonVisible] = useState(false);
    const [RiderVisible, setRiderVisible] = useState(false);
    const [PaymentReason, setPaymentReason] = React.useState([]);
    const [Premiums, setPremiums] = useState([]);
    const [IsPremiumDisabled, setIsPremiumDisabled] = useState(true);
    const [IsSumInsuredDisabled, setIsSumInsuredDisabled] = useState(true);
    const [IsPlanDisabled, setIsPlanDisabled] = useState(true);
    const [PremiumId, setPremiumId] = useState(0);
    const [UploadedFile, setUploadedFile] = useState(undefined);
    const [PlansList, setPlansList] = useState([]);

    const ResetValues = () => {
        setRenewDetailsList(initialRenewalInfo)
        setRenewalLead(0)
        setPaymentReason([])
        setUploadedFile(undefined)
        setReasonVisible(false)
    }

    const GetRenewDetails = (LeadId) => {
        GetRenewalDetails(LeadId).then((result) => {
            if (result) {
                setRenewDetails(result);
            }
        }).catch((e) => {
            console.log(e);
        })
    }

    const handleChange = (e) => {
        const { name, value } = e.target;

        // Handle special cases first
        switch (name) {
            case "LeadId":
                handleLeadChange(value);
                return;
            case "NoticePremium":
                handlePremiumChange(value);
                return;
            case "PremiumAmount":
            case "SumInsured":
                handleNumericChange(name, value);
                return;
            default:
                handleDefaultChange(name, value);
        }
    };

    // Helper functions
    const handleLeadChange = (value) => {
        setPremiums([]);
        setRenewalLead(value);
        setRenewDetailsList(initialRenewalInfo);
    };

    const handlePremiumChange = (value) => {
        const selectedPremium = Premiums[value];
        setPremiumId(selectedPremium.id);
        setRenewDetailsList(prev => ({
            ...prev,
            PremiumAmount: parseFloat(selectedPremium.value)
        }));
        setIsPremiumDisabled(selectedPremium.id !== 3);
    };

    const handleNumericChange = (name, value) => {
        const numericValue = parseFloat(value.replace(/[^0-9.]/g, ''));
        setRenewDetailsList(prev => ({
            ...prev,
            [name]: numericValue
        }));
    };

    const handleDefaultChange = (name, value) => {
        setRenewDetailsList(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const FetchPlans = () => {
        masterService.GetProductPlansFromCore(rootScopeService.getProductId(), RenewDetailsList.SupplierId, "PaymentLink").then(res => {
            setPlansList(res);
        })
    }

    const handleMultiple = (e) => {
        const { name, value, type } = e.target;

        if (name === 'PaymentReasonList') {
            const selectedReasons = value;
            
            // Handle visibility states
            const hasOthersReason = selectedReasons.some(item => item.Id === 29);
            const hasPlanChangeReason = selectedReasons.some(item => item.Id === 6);
            const hasRiderReason = selectedReasons.some(item => [10, 11].includes(item.Id));
            const hasSIChangeReason = selectedReasons.some(item => [4, 5].includes(item.Id));
    
            // Update UI states based on selected reasons
            setReasonVisible(hasOthersReason);
            setIsPlanDisabled(!hasPlanChangeReason);
            setRiderVisible(hasRiderReason);
            setIsSumInsuredDisabled(!hasSIChangeReason);
    
            // Reset related fields if reason is not selected
            const updatedDetails = { ...RenewDetailsList };
            
            if (!hasOthersReason) {
                updatedDetails.PaymentReason = '';
            }
            if (!hasPlanChangeReason) {
                updatedDetails.PlanID = RenewDetails.PlanID;
            }
            if (!hasRiderReason) {
                updatedDetails.RiderDetails = '';
            }
            if (!hasSIChangeReason) {
                updatedDetails.SumInsured = RenewDetails.SumInsured;
            }
    
            setRenewDetailsList(updatedDetails);
            setPaymentReason(selectedReasons);
        }
    };
    const formatPaymentReason = () => {
        const excludedReasons = ['1 Year', '2 Year', '3 Year', 'Others'];
        let reason = PaymentReason
            .filter(item => item.Name?.trim() && !excludedReasons.includes(item.Name.trim()))
            .map(item => item.Name)
            .join(', ');
    
        // Handle premium period reasons
        if ([0, 1, 2].includes(PremiumId)) {
            const periodMap = {
                0: '1 Year',
                1: '2 Year',
                2: '3 Year'
            };
            const periodText = periodMap[PremiumId];
            reason = reason ? `${reason}, ${periodText}` : periodText;
        }
    
        return reason;
    };

    const SetRenewalPaymentDetails = () => {
        let reason = formatPaymentReason();
        reason = RenewDetailsList.PaymentReason != '' ? reason.trim() + ', ' + RenewDetailsList.PaymentReason : reason;
        reason = RenewDetailsList.RiderDetails != '' ? reason.trim() + ', ( Riders added - ' + RenewDetailsList.RiderDetails + ')' : reason;

        if (RenewDetailsList.PremiumAmount > 500 && RenewalLead > 0) {
            let requestData = {
                "LeadId": RenewalLead,
                "PaymentReason": reason,
                "PremiumAmount": RenewDetailsList.PremiumAmount,
                "UploadedFile": UploadedFile,
                "PlanID": RenewDetailsList.PlanID,
                "SumInsured": RenewDetailsList.SumInsured
            }
            InsertRenewalPaymentDetails(requestData).then((result) => {
                if (result && result.ErrorCode == 2) {
                    enqueueSnackbar(result.Data, { variant: 'success', autoHideDuration: 3000, });
                }
                else if (result && result.ErrorCode == 1) {
                    enqueueSnackbar(result.Data, { variant: 'error', autoHideDuration: 3000, });
                }
                else {
                    enqueueSnackbar("Error", { variant: 'error', autoHideDuration: 3000, });
                }

            }).catch((e) => {
                console.log(e);
            })
            ResetValues()
        }
        else {
            enqueueSnackbar("Please select Amount > 500", { variant: 'error', autoHideDuration: 3000, });
        }
    }

    useEffect(() => {
        let LeadList = [];
        for (let key in allLeads) {
            if (allLeads[key].LeadSource === "Renewal"
                && allLeads[key].NoticePremium > 100
                && [1, 2, 3, 4, 11].indexOf(allLeads[key].StatusId) !== -1
                && !LeadList.includes(allLeads[key].LeadID)) {
                LeadList.push(allLeads[key].LeadID)
            }
        }
        if (LeadList.length === 1) {
            setRenewalLead(LeadList[0]);
        }
        else if (LeadList.length === 0) {
            LeadList.push("No Valid Leads")
        }
        else {
            setRenewalLead(LeadList[0]);
        }
        setvisibleLeads(LeadList);
    }, []);

    useEffect(() => {
        if (RenewalLead > 0) {
            GetRenewDetails(RenewalLead)
        }
    }, [RenewalLead])

    useEffect(() => {
        if (RenewDetailsList.SupplierId > 0) {
            FetchPlans()
        }
    }, [RenewDetailsList.SupplierId])

    useEffect(() => {
        let PremiumList = [];
        if (RenewDetails !== undefined && RenewDetails !== null) {
            PremiumList.push({ id: 0, value: RenewDetails.Premium, label: "One Year Premium" });
            PremiumList.push({ id: 1, value: RenewDetails.TwoYrPremium, label: "Two Year Premium" });
            PremiumList.push({ id: 2, value: RenewDetails.ThreeYrPremium, label: "Three Year Premium" });
            PremiumList.push({ id: 3, value: 0, label: "Custom" });
            setPremiumId(PremiumList[0].id);
            setPremiums(PremiumList)
            setIsPremiumDisabled(true);
            setRenewDetailsList({
                ...RenewDetailsList
                , ['PremiumAmount']: PremiumList[0].value
                , ['InsurerName']: RenewDetails.SupplierName
                , ['PlanName']: RenewDetails.Plan
                , ['ProposerName']: RenewDetails.ProposerName
                , ['PolicyNo']: RenewDetails.OldPolicyNo
                , ['SumInsured']: RenewDetails.SumInsured
                , ['SupplierId']: RenewDetails.SupplierID
                , ['PlanID']: RenewDetails.PlanID
            });
        }
    }, [RenewDetails]);

    const handleFileUpload = (event) => {
        let file = event.target.files[0];
        if (!file) {
            enqueueSnackbar("Select File", { variant: 'error', autoHideDuration: 3000, });
            return;
        }

        if (file.size > MAX_FILE_SIZE) {
            enqueueSnackbar("File size should not exceed 1MB", { variant: 'error', autoHideDuration: 3000, });
            return;
        }
        const extension = file.name.split('.').pop().toLowerCase();
        if (VALID_IMAGE_EXTENSIONS.includes(extension)) {
            setUploadedFile(file)
        }
        else {
            enqueueSnackbar("Please upload an image file.", { variant: 'error', autoHideDuration: 3000, });
        }
    };

    return (
        <>
            {RenewDetailsList && <>

                    <Grid container spacing={2}>
                        <SelectDropdown
                            name="LeadId"
                            label="Select LeadId"
                            value={RenewalLead}
                            options={visibleLeads}
                            labelKeyInOptions='_all'
                            valueKeyInOptions='_all'
                            handleChange={handleChange}
                            show={true}
                            sm={12} md={12} xs={12}
                        />
                        
                        <TextInput
                            name="InsurerName"
                            label="Insurer Name"
                            value={RenewDetailsList.InsurerName}
                            disabled='true'
                            className="custom-disabled-input"
                            sm={12} md={6} xs={12}
                        />
                        <SelectDropdown
                            name="PlanID"
                            label="Plan"
                            value={RenewDetailsList.PlanID}
                            options={PlansList}
                            labelKeyInOptions='PlanName'
                            valueKeyInOptions='OldPlanId'
                            handleChange={handleChange}
                            sm={12} md={6} xs={12}
                            disabled={IsPlanDisabled}
                        />
                        <TextInput
                            name="ProposerName"
                            label="Proposer Name"
                            value={RenewDetailsList.ProposerName}
                            disabled='true'
                            className="custom-disabled-input"
                            sm={12} md={6} xs={12}
                        />
                         <TextInput
                            name="SumInsured"
                            label="Sum Insured"
                            type="text"
                            value={RenewDetailsList.SumInsured}
                            disabled={IsSumInsuredDisabled}
                            className={IsSumInsuredDisabled ? "custom-disabled-input" : ""}
                            handleChange={handleChange}
                            sm={12} md={6} xs={12}
                        />
                        <SelectDropdown
                            name="NoticePremium"
                            label="Premium"
                            value={PremiumId}
                            options={Premiums}
                            labelKeyInOptions='label'
                            valueKeyInOptions='id'
                            handleChange={handleChange}
                            show={true}
                            sm={12} md={6} xs={12}
                        />
                        <TextInput
                            name="PremiumAmount"
                            label="Premium Amount"
                            type="text"
                            value={RenewDetailsList.PremiumAmount}
                            disabled={IsPremiumDisabled}
                            className={IsPremiumDisabled ? "custom-disabled-input" : ""}
                            handleChange={handleChange}
                            sm={12} md={6} xs={12}
                        />
                        <TextInput
                            name="PolicyNo"
                            label="Policy Number"
                            value={RenewDetailsList.PolicyNo}
                            disabled='true'
                            className="custom-disabled-input"
                            sm={12} md={6} xs={12}
                        />
                        <MultipleSelectDropdown
                            name="PaymentReasonList"
                            label="Payment Reason"
                            value={PaymentReason}
                            options={MultipleReasonsType}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='_all'
                            handleChangeMultiple={handleMultiple}
                            renderValue={(selected) => selected.map((s) => s.Name).join(", ")}
                            show={true}
                            sm={12} md={6} xs={12}
                        />
                        <TextInput
                            name="PaymentReason"
                            label="Changes/Reason"
                            value={RenewDetailsList.PaymentReason}
                            handleChange={handleChange}
                            sm={12} md={6} xs={12}
                            show={ReasonVisible}
                        />
                        <TextInput
                            name="RiderDetails"
                            label="Rider Details"
                            value={RenewDetailsList.RiderDetails}
                            handleChange={handleChange}
                            sm={12} md={6} xs={12}
                            show={RiderVisible}
                        />
                       <Grid item sm={12} md={12} xs={12}>
                     
                     <div className="upload-option">
                         <div className="upload-section">
                             <Typography variant="caption" className="upload-label">
                                 Upload Attachment
                             </Typography>
                             <input
                                 type="file"
                                 id="hiddenFileInput1"
                                 onChange={handleFileUpload}
                                 className="hidden-file-input"
                             />
                             <label htmlFor="hiddenFileInput1" className="upload-button-container">
                                 <Button
                                     variant="outlined"
                                     component="span"
                                     className="compact-upload-btn"
                                     startIcon={<span className="upload-icon"><FileUploadIcon/></span>}
                                 >
                                     Choose File to Upload
                                 </Button>
                             </label>
                             {UploadedFile && UploadedFile.name && (
                                 <div className="uploaded-file-container">
                                     <Typography variant="caption" className="uploaded-file-label">
                                          File attached: {UploadedFile.name}
                                     </Typography>
                                 </div>
                             )}
                         </div>
                     </div>

                    </Grid>
                        <Grid item sm={12} md={12} xs={12} className="text-center">
                            <Button onClick={SetRenewalPaymentDetails} variant="outlined" className="SubmitBtn" >
                                Submit Request
                            </Button>
                    </Grid>
                </Grid>

              


                <p className="Note">Note : Please note that the generated link will be send to the customer on the primary contact details</p>

            </>
            }
        </>

    );
}
export default RenewalPaymentLink
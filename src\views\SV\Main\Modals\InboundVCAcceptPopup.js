import React, { useState, useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { CONFIG, SV_CONFIG } from "../../../../appconfig";
import { CALL_API } from "../../../../services";
import { setCookie } from "../../../../store/token-store/Token-Store";
import { Grid, LinearProgress } from "@mui/material";
import withStyles from '@mui/styles/withStyles';
import ModalPopup from "../../../../components/Dialogs/ModalPopup";
import User from "../../../../services/user.service";
import Common from "../helpers/Common";
import { setRefreshLead } from "../../../../store/actions";

const BorderLinearProgress = withStyles((theme) => ({
    root: {
      height: 20,
      borderRadius: 40,
    
    },
    colorPrimary: {
      backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 200 : 700],
    },
    bar: {
      borderRadius: 40,
      backgroundColor: '#EB6464',
    },
  }))(LinearProgress);

let AUTO_ACCEPT_TIMER = 10;

const InboundVCAcceptPopup = (props) => {
	if (User.UserQueue && (User.UserQueue.includes("healthunderwriters")|| User.UserQueue.includes("vchealthped"))) {
		AUTO_ACCEPT_TIMER = SV_CONFIG.VCJoinTimeDoc || 6;
	}
    
    const { showInboundVCPopup = true, setShowInboundVCPopup, customerName, leadId } = props;
    const [ showMainPopup, setShowMainPopup ] = useState(true);
    const [ secondsLeft, setSecondsLeft ] = useState(AUTO_ACCEPT_TIMER);

    const ibvcData = localStorage.getItem('IBVCData')
    const ibvcDataParsed = JSON.parse(ibvcData);

    const timerRef = useRef(null);

    const dispatch = useDispatch()

    useEffect(() => {
        timerRef.current = setInterval(() => {
            setSecondsLeft((secondsLeft) => secondsLeft - 1);
        }, 1000);
    
        return () => {
            clearInterval(timerRef.current);
        };
    }, []);
    
    useEffect(() => {
        if (secondsLeft === 0) {
            handleAgentJoin();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [secondsLeft]);

    const getAgentLinkVCIB = async () => {
      let agentLink = null;
      let agentSid = null;
      let error = null;

      const UserName = User.UserName;
      const EmployeeId = User.EmployeeId;
  
      const maxAttempts = 2;
      let retryCount = 0;
  
      while (retryCount < maxAttempts) {
          try {
              retryCount++;
              const apiInput = {
                  url: 'api/SalesView/GetAgentURLByMeetingId',
                  method: 'POST',
                  service: 'MatrixCoreAPI',
                  requestData: {
                      LeadId: ibvcDataParsed.leadId,
                      VcMeetingId: ibvcDataParsed.meetingId,
                      AgentName: UserName,
                      EmployeeId: EmployeeId
                  }
              };
              const apiResult = await CALL_API(apiInput);
              console.log("getAgentURLByMeeting response: ", apiResult);
              if (apiResult.success) {
                  agentLink = apiResult.data.agentLink;
                  agentSid = apiResult.data.agentSid;
                  break;
              } else {
                  error = apiResult.message;
              }
          } catch (err) {
              console.error("getAgentURLByMeeting error: ", err);
              error = err.message;
          }
      }
  
      return { agentLink, agentSid, error };
    };
  

    const handleOpenSalesView = () => {

      const input = {
        url: `api/SalesView/GetLeadCustomerDetails/${leadId}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };

      let customerId;
      let productId;
      
      CALL_API(input).then((JsonData) => {
        customerId = JsonData.LeadData.CustID;
        productId = JsonData.LeadData.ProductID;

        Common.OpenSalesView(customerId, productId, leadId, null);
        dispatch(setRefreshLead({ RefreshLead: true }));
        localStorage.removeItem("RemoteFriendlyLeadId");
      })
    }

    const handleAgentJoin = async () => {
        clearInterval(timerRef.current);

        const toOpenSVLead = false;
        window.sipAccept(toOpenSVLead);
        const { agentLink, agentSid } = await getAgentLinkVCIB(); // Getting error object when agentLink is null
        // TODO: store this error in ga
        if (agentLink) {
            handleOpenSalesView();
            setCookie('isPbmeetV3', true, 1, '.policybazaar.com');
            setCookie('VC_IB_CONFIG',JSON.stringify({'cam': true}), 1/24, '.policybazaar.com' );
            setCookie('agentSid', agentSid, 1, '.policybazaar.com');
            const currentDate = new Date();
            setCookie('lastActiveVC', currentDate.getTime(), 2 / 1440, '.policybazaar.com');
            setTimeout(() => {
                window.open(agentLink);
            }, 1000)
        }
        localStorage.removeItem('IsInboundVC');
        setShowMainPopup(false);
        setShowInboundVCPopup(false);
    }

    return (
        <>
          {showMainPopup && (
            <ModalPopup
              open={showInboundVCPopup}
              className=""
              disableBackdropClick={true}
              handleClose={() => {
                setShowInboundVCPopup(false);
              }}
            >
              <div className="IBVCcallAlertSection">
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <div className="IBVCcallBgImage">
                      <img src={CONFIG.PUBLIC_URL + "/images/salesview/OBJECTS.svg"} alt="object" />
                    </div>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <div className="text-center">
                      <p>
                        <img src={CONFIG.PUBLIC_URL + "/images/salesview/awaiting.gif"} alt="awaiting" />Awaiting your presence!
                      </p>
                      <div className="deatils">
                        Your Customer <b> {customerName ? customerName.substring(0, 30) : null}-{leadId} </b> is
                        <b> waiting to connect </b> with you on a <h3>Video Call.</h3>
                      </div>
                      <div className="auto-redirect-msg">
                        You will be auto redirected to PB Meet in next
                      </div>
                      <div className="LinearProgess">
                        <BorderLinearProgress variant="determinate" value={(secondsLeft * 100)/AUTO_ACCEPT_TIMER} /> <span>{secondsLeft}</span>
                      </div>
                      <button onClick={handleAgentJoin}>Join now</button>
                      {/* <div className="minimizeBtn" onClick={handleMinimize}>
                        <img src={CONFIG.PUBLIC_URL + "/images/salesview/minimize.svg"} alt="minimize" />Minimize
                      </div> */}
                    </div>
                  </Grid>
                </Grid>
              </div>
            </ModalPopup>
          )}
        </>
      );
      
};

export default InboundVCAcceptPopup;

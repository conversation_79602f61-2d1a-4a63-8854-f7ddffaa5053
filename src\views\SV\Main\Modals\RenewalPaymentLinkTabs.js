import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import React from "react";
import RenewalPaymentLink from "../Modals/RenewalPaymentLink";
import RenewalPaymentLinkHistory from "../Modals/RenewalPaymentLinkHistory";
import PropTypes from "prop-types";
import { Box, Tab, Tabs } from "@mui/material";
import './RenewalPaymentLink.scss';
function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && <Box p={3}>{children}</Box>}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.any.isRequired,
    value: PropTypes.any.isRequired,
};

export const RenewalPaymentLinkTabs = (props) => {
    const { handleClose, open } = props;
    const [activeTab, setActiveTab] = React.useState(1);

    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };

    return (
        <ModalPopup className="RenewalLinkPopup" title="Renewal Payment Links" open={open} handleClose={handleClose}>
          
            <Tabs
                value={activeTab}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                className="tabs"
            >
                <Tab label="View Request History" />
                <Tab label="Create New Link" />

            </Tabs>

            <TabPanel value={activeTab} index={0}>
                <RenewalPaymentLinkHistory/>
            </TabPanel>

            <TabPanel value={activeTab} index={1}>
                <RenewalPaymentLink/>
            </TabPanel>

        </ModalPopup>
    );
}

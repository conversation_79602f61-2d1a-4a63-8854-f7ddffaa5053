import { OpenInNewRounded } from "@mui/icons-material";
import { useSnackbar } from "notistack";
import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { SV_CONFIG } from "../../../../../appconfig";
import { CALL_API } from "../../../../../services";
import User from "../../../../../services/user.service";
import { setOpenRightBarMenu } from "../../../../../store/actions";
import { default as Data } from "../../../../../assets/json/StaticData";
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import makeStyles from '@mui/styles/makeStyles';
import { Badge } from "@mui/material";
import {localStorageCache} from '../../../../../../src/utils/utility';
import { getMongoNotificationData } from "../../../../../helpers/commonHelper";

const useStyles = makeStyles((theme) => ({
    root: {
        width: '100%',
    },
    heading: {
        fontSize: theme.typography.pxToRem(15),
        flexBasis: '33.33%',
        flexShrink: 0,
    },
    secondaryHeading: {
        fontSize: theme.typography.pxToRem(15),
        color: theme.palette.text.secondary,
    },
}));


export const SortNotificationData = (notificationData) => {
    if (notificationData) {
        const SortedData = notificationData.sort((a, b) => Number(b.ts) - Number(a.ts));

        return SortedData.map(data => {
            if (data && data.ts && (data.ts).length >= 14) {
                data = {
                    ...data, ts: new Date((data.ts).substring(0, 4), ((data.ts).substring(4, 6)) - 1, (data.ts).substring(6, 8),
                        (data.ts).substring(8, 10), (data.ts).substring(10, 12),
                        (data.ts).substring(12, 14)).toString().slice(0, 25)
                }
            };
            var stringArray = data.ts.split(/(\s+)/);
            data = {
                ...data, ts: stringArray[0] + " " + stringArray[4] + " " + stringArray[2] + " " +
                    stringArray[6] + " " + stringArray[8]
            }
            return data;
        }
        );
    }
}

const Notifications = (props) => {
    const dispatch = useDispatch();
    let notificationDatafromRedux = useSelector(state => state.salesview.notificationData);
    let notificationDataNewFromRedux = useSelector(state => state.salesview.notificationDataNew);
    let showNewNotificationPanel = useSelector(state => state.salesview.showNewNotificationPanel);
    let NotificationTypes = Data.NotificationTypes;

    const [expanded, setExpanded] = useState(false);
    const [notificationData, setNotificationData] = useState([]);
    const [notificationDataNew, setNotificationDataNew] = useState([]);
    const { enqueueSnackbar } = useSnackbar();
    const [MongonotificationData, setMongonotificationData] = useState([]);
    const [IsMongoDataSource, setIsMongoDataSource] = useState(true);
    const classes = useStyles();

    const redirectNotificationURL = function (data) {
        let MongoNotificationData = localStorageCache.readFromCache('MongoNotificationData') != null ? JSON.parse(localStorageCache.readFromCache('MongoNotificationData')) : [];
        if (MongoNotificationData && Array.isArray(MongoNotificationData)) {
            MongoNotificationData.forEach((item, index) => {
                if (item.id == data.id) {
                    item.IsRead = true;
                }
            })
            localStorageCache.writeToCache("MongoNotificationData", JSON.stringify(MongoNotificationData), 5 * 60 * 60 * 1000);
        }
        if (data.link != '' && data.link != undefined) {
            window.open(data.link);
            data.IsRead = true;
            markReadNotification(data.id);
        }
        else if (data && data.type === "HWPitchPlan") {
            data.IsRead = true;
            markReadNotification(data.id);
            props.handleClose();
            // openHIResponse();
        }
        else if (data.lead && data.lead.CustomerId && data.lead.ProductId && data.lead.LeadId) {
            // $scope.divNotification = true;
            //$window.open(link);
            // OpenSalesView(data.lead.CustomerId, data.lead.ProductId, data.lead.LeadId, 'o', 'Cancel Booked Lead', '53');
            data.IsRead = true;
            markReadNotification(data.id);
        }

        else {
            data.IsRead = true;
            markReadNotification(data.id);
            enqueueSnackbar("No link found", { variant: 'error', autoHideDuration: 3000, });

            // divNotification = true;
        }

        if (showNewNotificationPanel && data && data.id && data.IsRead)
            BindAllNotifications(data.id, data.IsRead);
    }

    const markReadNotification = function (NotificationId) {
        let input = {};
        if (showNewNotificationPanel) {
            input = {
                url: "api/WebSiteService/PushReadNotification",
                method: 'POST',
                service: 'MatrixCoreAPI',
                requestData: { "notificationId": NotificationId, "userId": User.UserId }
            }
            CALL_API(input);
        }
        else {
            input = {
                url: SV_CONFIG["CustomerNotificationURL"][SV_CONFIG["environment"]] + "customer/setreadnotification",
                method: 'POST', service: 'custom',
                requestData: { "notification": NotificationId, "agent": User.UserId }
            };
            CALL_API(input);
            input = {
                url: "api/WebSiteService/PushReadNotification",
                method: 'POST',
                service: 'MatrixCoreAPI',
                requestData: { "notificationId": NotificationId, "userId": User.UserId }
            }
            CALL_API(input);
        }
    }

    const CustomerNotification = () => {
        if (Array.isArray(notificationDatafromRedux)) {
            try {
                const sortednotificationData = SortNotificationData(notificationDatafromRedux);
                setNotificationData(sortednotificationData ? sortednotificationData : []);
            }
            catch (err) {
                console.log(err);
                setNotificationData(notificationDatafromRedux && notificationDatafromRedux != undefined ? notificationDatafromRedux : []);
            }
        }
        else {
            setNotificationData(notificationDatafromRedux && notificationDatafromRedux != undefined ? notificationDatafromRedux : []);
        }
    }

    const BindAllNotifications = (id = null, isRead = false) => {
        if (notificationDataNewFromRedux && Array.isArray(notificationDataNewFromRedux)) {
            try {
                let nDataNew = notificationDataNewFromRedux;
                nDataNew.forEach(function (item) {
                    delete item.dateTime;
                    delete item.empID;
                    if (id && isRead && (item.id == id)) {
                        item.IsRead = isRead;
                    }
                });

                let allNotifications = [...nDataNew];

                if (Array.isArray(notificationDatafromRedux) && notificationDatafromRedux.length > 0) {
                    notificationDatafromRedux.forEach(element => {
                        element.notificationType = 0;
                        if (allNotifications.findIndex((item) => item.id === element.id) === -1) {
                            allNotifications.push(element);
                        }
                    });
                }

                setNotificationDataNew(allNotifications ? allNotifications : []);
            }
            catch (err) {
                console.log(err);
                setNotificationDataNew(notificationDatafromRedux ? notificationDatafromRedux : []);
            }
        }
        else {
            setNotificationDataNew(notificationDatafromRedux ? notificationDatafromRedux : []);
        }
    }

    const GetSortedMongoNotification = () => {
        let MongoNData = getMongoNotificationData();
        if (Array.isArray(MongoNData) && MongoNData.length > 0) {
            try {
                const sortednotificationData = SortNotificationData(MongoNData);
                console.log(sortednotificationData);
                setMongonotificationData(sortednotificationData ? sortednotificationData : []);
            }
            catch (err) {
                console.log(err);
                setMongonotificationData(MongoNData && MongoNData != undefined ? MongoNData : []);
            }
        }
        else {
            setMongonotificationData(MongoNData && MongoNData != undefined ? MongoNData : []);
        }
    }

    useEffect(() => {
        CustomerNotification();
        if (showNewNotificationPanel) {
            BindAllNotifications();
        }
        let MongoNotificationData = getMongoNotificationData();
        setMongonotificationData(MongoNotificationData);
        GetSortedMongoNotification();
        let IsMongoNotification = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.IsMongoNotification) || SV_CONFIG.IsMongoNotification;
        if (IsMongoNotification != true) {
            setIsMongoDataSource(false);
        }
    }, [])

    const SetIcon = (input) => {
        if (input.includes('genie')) {
            return 'notification';
        }

        switch (input) {
            case 'NotifyIncentive':
                return 'notify'
            case 'notification':
                return 'notification'
            case 'announcement':
                return 'annoucement'
            case 'alert':
                return 'alert';
            default:
                return 'alert';
        }
    }

    const formatHeading = (input) => {
        if (input.includes('genie')) {
            return 'notification';
        }
        
        switch (input) {
            case 'HWPitchPlan':
                return 'Housewife Plan Alert'
            default:
                return input;
        }
    }

    const openHIResponse = () => {
        dispatch(setOpenRightBarMenu({ OpenRightBarMenu: "HouseWife" }));
    }

    const handleChangeAccordian = (panel) => (event, isExpanded) => {
        setExpanded(isExpanded ? panel : false);
    };

    const GetNotificationType = (notificationType) => {
        let notificationTypeHeader = '';
        NotificationTypes.forEach(function (item) {
            if (item.Id == notificationType) {
                notificationTypeHeader = item.Name;
            }
        });
        return notificationTypeHeader;
    };

    const BindNotification = ({ data, isRead }) => {
        return <>
            <li key={data.id}
                onClick={() => { redirectNotificationURL(data) }}
                className={isRead ? "list-item" : "list-item active"}
                style={
                    {
                        background: isRead ? '#eaeaea' : '#c8dbf9',
                        color: '#253858'
                    }
                }
            >
                <div className={`icon ${SetIcon(data.type)}`}>
                    <span className="ico"></span>
                </div>
                <div className="content-block">
                    <p className="head">{formatHeading(data.type)}</p>
                    <p className="Notificationtime">{data.ts}</p>
                    <p class="text">{data.text}</p>
                </div>
                {data.link && < OpenInNewRounded />}
            </li>
        </>
    }

    const GetNotificationDataComponent = ({ notificationDataInput }) => {

        return <ul style={{ background: '#fff', color: '#000' }}>
            {
                notificationDataInput.map((data) => (
                    !data.IsRead ?
                        <BindNotification
                            data={data}
                            isRead={false}
                        />
                        : null
                ))
            }
            {notificationDataInput.map((data) => (
                data.IsRead ?
                    <BindNotification
                        data={data}
                        isRead={true}
                    />
                    : null
            ))}
        </ul>
    }

    const NotificationToggleData = ({ notificationDataInput, toggleName }) => {
        let unreadNotifications = Array.isArray(notificationDataInput) ? notificationDataInput.filter(item => item.IsRead !== true).length : 0;

        return <>
            <Accordion
                expanded={expanded === toggleName}
                onChange={handleChangeAccordian(toggleName)}
                className="NotificationAccordian"
            >
                <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                >
                    <Typography className={classes.heading}>
                        {unreadNotifications > 0 &&
                            <Badge variant="standard" color="secondary" badgeContent={unreadNotifications}>
                            </Badge>
                        }
                        {toggleName}
                    </Typography>
                </AccordionSummary>
                <AccordionDetails>
                    <ul>
                        {notificationDataInput.length > 0
                            ?
                            <>
                                {notificationDataInput.map((data) => (
                                    !data.IsRead &&
                                    <BindNotification
                                        data={data}
                                        isRead={data.IsRead}
                                    />
                                ))}
                                {notificationDataInput.map((data) => (
                                    data.IsRead &&
                                    <BindNotification
                                        data={data}
                                        isRead={data.IsRead}
                                    />
                                ))}
                            </>
                            : <h2 className="NoNewNotification">No New Notification</h2>
                        }
                    </ul>
                </AccordionDetails>
            </Accordion>
        </>
    }

    const GetSortedNewNotifications = (notificationDataInput, notificationType) => {
        let allNotifications = [];
        notificationDataInput.forEach(function (item) {
            if (item.notificationType == notificationType) {
                allNotifications.push(item);
            }
        });
        allNotifications = SortNotificationData(allNotifications);
        return allNotifications;
    }

    const GetNewNotificationDataComponent = ({ notificationDataInput }) => {
        return <>
            <NotificationToggleData
                notificationDataInput={GetSortedNewNotifications(notificationDataInput, 2)}
                toggleName={"Quotes"}
            />
            <NotificationToggleData
                notificationDataInput={GetSortedNewNotifications(notificationDataInput, 1)}
                toggleName={"Email"}
            />
            <NotificationToggleData
                notificationDataInput={GetSortedNewNotifications(notificationDataInput, 3)}
                toggleName={"Whatsapp"}
            />
            <NotificationToggleData
                notificationDataInput={GetSortedNewNotifications(notificationDataInput, 4)}
                toggleName={"Customer"}
            />
            <NotificationToggleData
                notificationDataInput={GetSortedNewNotifications(notificationDataInput, 0)}
                toggleName={"Other"}
            />
        </>
    }

    return (
        <>
            {(MongonotificationData.length > 0 || notificationDataNew.length > 0 || notificationData.length > 0) ?
                <div>
                    {
                        showNewNotificationPanel ?
                            <GetNewNotificationDataComponent
                                notificationDataInput={notificationDataNew}
                            />
                            :
                            <GetNotificationDataComponent
                                notificationDataInput={IsMongoDataSource == true ? MongonotificationData : notificationData}
                            />
                    }
                </div>
                : <h2 style={{ color: "black", textAlign: "center" }}>No New Notification</h2>}
        </>
    )
}

export default Notifications;
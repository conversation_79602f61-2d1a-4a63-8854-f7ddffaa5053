import { LinearProgress } from "@mui/material";
import { useSnackbar } from "notistack";
import React, { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { CALL_API } from "../../../services";
import rootScopeService from "../../../services/rootScopeService";
import User from "../../../services/user.service";

const getCommBoxURLService = (LeadId) => {
    const input = {
        url: `api/SalesView/GenerateComBoxUrl`,
        service: 'MatrixCoreAPI', timeout: 's',
        method: 'POST',
        requestData: {
            LeadId,
            UserId: User.UserId,
            ProductId: rootScopeService.getProductId(),
            AgentName: User.UserName,
            TypeId: 0,  // todo confirm
            GroupIds: GetGroups()
        }
    }
    return CALL_API(input);
}

const GetGroups = () => {
    let groupIds = [];
    if (rootScopeService.getProductId() === 2 && [12,13].indexOf(User.RoleId) > -1)
    {
        let groupList = User.UserGroupList;
        if(Array.isArray(groupList))
        {
            for (var i = 0, len = groupList.length; i < len; i++) {
                groupIds.push(groupList[i].GroupId);
            }
        }
    }
    return groupIds;
}

const STATE_KEYS = {
    LOADING: 'LOADING',
    LOADED: 'LOADED',
    ERROR: 'ERROR',
}

export default function Messages(props) {
    let { parentLeadId } = useSelector(({ salesview }) => salesview);
    const [BMSUrl, setBMSUrl] = useState('');
    // let [url, setUrl] = useState(GetIframeURL("Message", parentLeadId));

    const [urlState, setUrlState] = useState(STATE_KEYS.LOADING);
    const { enqueueSnackbar } = useSnackbar()
    const getCommBoxURL = useCallback(async () => {
        try {
            setUrlState(STATE_KEYS.LOADING);
            let url = await getCommBoxURLService(parentLeadId);
            if (url && url.InboxURL) {
                url = url.InboxURL
            }
            if (url && url.includes && url.includes('https://')) {
                setBMSUrl(url);
                setUrlState(STATE_KEYS.LOADED);
            }
            else {
                enqueueSnackbar('Unable to open BMS', {
                    variant: "error",
                    autoHideDuration: 3000
                });
                setUrlState(STATE_KEYS.ERROR);
            }
        }
        catch {
            setUrlState(STATE_KEYS.ERROR);
        }
    }, [enqueueSnackbar, parentLeadId])
    useEffect(() => {
        getCommBoxURL();
    }, [])
    return (
        <>
            {(urlState === STATE_KEYS.LOADING) && <LinearProgress color="secondary" />}
            {(urlState === STATE_KEYS.ERROR) && <p>Unable to Load CommBox, leadid: {parentLeadId} </p>}
            {(urlState === STATE_KEYS.LOADED) &&
                <div className="" style={{ width: '100%', height: '100%' }}>
                    <iframe
                        src={BMSUrl}
                        // src={url}
                        title="MailBox"
                        style={{ width: '100%', height: '100%' }}
                        allow="clipboard-read; clipboard-write"
                    />
                </div>
            }
        </>
    )

}

import { ExpandMore } from "@mui/icons-material";
import { useSnackbar } from "notistack";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { SV_CONFIG } from "../../../appconfig";
import SelectDropdown from "../../../components/SelectDropdown";
import { CALL_API } from "../../../services/api.service";
import { SetCustomerComment, SetLeadAudit } from "../../../services/Common";
import rootScopeService from "../../../services/rootScopeService";
import User from "../../../services/user.service";
import { ToggleButton, ToggleButtonGroup, Switch } from '@mui/material';

const Plans = [
    { "Id": 46547, "Name": "ReAssure 2.0 Bronze+" }
    , { "Id": 44426, "Name": "ReAssure 2.0 Platinum+" }
    , { "Id": 20344, "Name": "Health Companion" }
];

const CareSupremePlans = [
    { "Id": 39104, "Name": "Care Supreme Direct" }
    , { "Id": 38312, "Name": "Care Supreme" }
];
const CareSupremeValuePlans = [
    { "Id": 79189, "Name": "Care Supreme Value" }
    , { "Id": 101884, "Name": "Care Supreme Value (Direct)" }
];

let HealthRenewalInfo = {
    NoCostEMI: null,
    PaymentMode: 0,
    GSTRefundEligible: false,
    IsPlanMigrationReq: false,
    IsCarePlanMigrationReq: false,
    MigrationPlanId: 0,
    CareMigrationPlanId: 0,
    AllowedDiscount: null
};

// let FilterDropdown = [
//     { id: '0', name: 'NA' },
//     { id: '1', name: 'Monhtly' },
//     { id: '2', name: 'Quaterly' },
//     { id: '3', name: 'HalfYearly' },
//     { id: '4', name: 'Annually' }
// ];

let FilterDropdown = [
    { id: '0', name: 'NA' },
    { id: '1', name: 'Monhtly' },
    { id: '2', name: 'Quaterly' }
];

const RenewalAddInfo = () => {
    const { enqueueSnackbar } = useSnackbar();
    const [show, setShow] = useState(false);
    const [IsNoCostEMI, setIsNoCostEMI] = useState(false);
    const [IsMonthlyMode, setIsMonthlyMode] = useState(false);
    const [IsGSTRefundEligible, setIsGSTRefundEligible] = useState(false);
    const [PlanMigrationEligible, setPlanMigrationEligible] = useState(false);
    const [CarePlanMigrationEligible, setCarePlanMigrationEligible] = useState(false);
    const [AllowedDiscountEligible, setAllowedDiscountEligible] = useState(false);
    const [CarePlans, setCarePlans] = useState([]);
    const [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    const [PrimaryLeadId, allLeads, IsRenewal] = useSelector(state => {
        let { primaryLeadId, allLeads, IsRenewal } = state.salesview;
        return [primaryLeadId, allLeads, IsRenewal];
    });
    const [originalInfo, setOriginalInfo] = useState(HealthRenewalInfo);
    const [NewHealthRenewalInfo, setNewHealthRenewalInfo] = useState(HealthRenewalInfo);
    const [alignment, setAlignment] = React.useState('center');
    const [RenewalLead, setRenewalLead] = useState(0);
    const visibleLeads = [];
    for (let key in allLeads) {
        if (allLeads[key].LeadSource == "Renewal") {
            visibleLeads.push(allLeads[key].LeadID)
        }
    }
    const ShowGST = SV_CONFIG["ShowGST"];


    const ShowMonthlymode = () => {
        const MonthlyModeUsers = SV_CONFIG["MonthlyModeUsers"];
        if ((Array.isArray(MonthlyModeUsers) && MonthlyModeUsers.includes(User.EmployeeId)) || User.RoleId === 2)
            setIsMonthlyMode(true)
    }

    const showPaymentMode = SV_CONFIG["showPaymentMode"];
    const ValidateforEditRenewal = () => {
        var isRenewalAgent = false;
        var UserBUMappingList = User.UserBUMapping;
        if (UserBUMappingList !== undefined && UserBUMappingList !== "") {
            UserBUMappingList.forEach(function (val, key) {
                if (val.IsRenewal && ([106, 118, 130, 2].indexOf(val.ProductId) !== -1)) {
                    isRenewalAgent = true;
                }
            });
        }
        return isRenewalAgent;
    }

    const checkLeadEligibleFields = (value) => {
        setIsGSTRefundEligible(false);
        setPlanMigrationEligible(false);
        setAllowedDiscountEligible(false);
        for (let key in allLeads) {
            if (allLeads[key].LeadID == value && allLeads[key].GSTRefundEligible) {
                setIsGSTRefundEligible(true);
            }
            if (allLeads[key].LeadID == value && allLeads[key].RenewalSupplierId == 18) {
                setPlanMigrationEligible(true);
            }
            if (allLeads[key].LeadID == value && allLeads[key].RenewalSupplierId == 17) {
                setAllowedDiscountEligible(true);
            }
            if (allLeads[key].LeadID == value && allLeads[key].RenewalSupplierId == 3) {
                setAllowedDiscountEligible(true);
            }
            if (allLeads[key].LeadID == value && allLeads[key].RenewalSupplierId == 54 && allLeads[key].IsABHIDiscount == true && User.RoleId !== 13) {
                setAllowedDiscountEligible(true);
            }
        }
    }

    const handleChange = (e) => {
        const { name, value, type } = e.target;

        if (name === "RenewalLead") {
            setRenewalLead(value);
            checkLeadEligibleFields(value);
        }
        if (name === "PaymentMode" || name === "MigrationPlanId" || name === "CareMigrationPlanId") {
            setNewHealthRenewalInfo({ ...NewHealthRenewalInfo, [name]: value });
        }
        if (name === "GSTRefundEligible" || name === "AllowedDiscount") {
            setNewHealthRenewalInfo({ ...NewHealthRenewalInfo, [name]: !NewHealthRenewalInfo[name] });
        }
        if (name === "IsPlanMigrationReq") {
            if (NewHealthRenewalInfo[name] === true) {
                NewHealthRenewalInfo.MigrationPlanId = 0;
            }
            setNewHealthRenewalInfo({ ...NewHealthRenewalInfo, [name]: !NewHealthRenewalInfo[name] });
        }
        if (name === "IsCarePlanMigrationReq") {
            if (NewHealthRenewalInfo[name] === true) {
                NewHealthRenewalInfo.CareMigrationPlanId = 0;
            }
            setNewHealthRenewalInfo({ ...NewHealthRenewalInfo, [name]: !NewHealthRenewalInfo[name] });
        }
    }

    const SaveComment = (Comment) => {
        let UserId = User.UserId;
        var requestData = {
            "CustomerId": rootScopeService.getCustomerId(),
            "ProductId": rootScopeService.getProductId(),
            ParentLeadId: RenewalLead,
            PrimaryLeadId,
            UserId,
            "Comment": Comment,
            "EventType": 12
        };
        SetCustomerComment(requestData);
    }
    const SetLeadAudits = (NewInfo, oldInfo) => {
        let FieldsData = require("../../../assets/json/FieldsData").default;
        let reqDataAudit = [];
        let invalid = [undefined, null, 0, "", "0"];
        for (const [key, vdata] of Object.entries(NewInfo)) {
            var fieldVal = FieldsData.Field[key];
            if (vdata != oldInfo[key] && fieldVal !== undefined && !(invalid.indexOf(vdata) >= 0 && invalid.indexOf(oldInfo[key]) >= 0)) {
                reqDataAudit.push({
                    LeadId: RenewalLead,
                    AgentId: User.UserId,
                    SectionName: "RenewalAddInfo",
                    Field: fieldVal,
                    OldValue: oldInfo[key],
                    NewValue: vdata,
                    ProductId: rootScopeService.getProductId()
                });
            }
        }

        if (reqDataAudit.length > 0) {
            SetLeadAudit(reqDataAudit).then((result) => {
                enqueueSnackbar("Lead Audit Details Saved", { variant: 'success', autoHideDuration: 3000, });
            }, function () {
                console.log("Lead Audit not saved");
            })
        }
    }

    useEffect(() => {
        if (RenewalLead != 0)
            getHealthRenewalInfo()
    }, [RenewalLead]);

    const handleAlignment = (event, newAlignment) => {
        setAlignment(newAlignment);
        let value = 0;
        if (newAlignment === "left") {
            value = 2
        }
        else if (newAlignment === "right") {
            value = 1
        }
        setNewHealthRenewalInfo({ ...NewHealthRenewalInfo, ["NoCostEMI"]: value });
    };

    const setHealthRenewalNeedAnalysisService = (reqData) => {
        const input = {
            url: `coremrs/api/HealthRenewal/SetHealthRenewalNeedAnalysis`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData
        };
        return CALL_API(input);
    }
    const getHealthRenewalInfo = () => {
        if (RenewalLead > 0) {
            setIsNoCostEMI(true);

            if (SV_CONFIG["NoCostEnableUsers"][SV_CONFIG["environment"]].indexOf(User.UserId) !== -1) {
                setIsNoCostEMI(false);
            }
            else if (User.GroupProcessId === "4") {
                setIsNoCostEMI(false);
            }
            else if (ValidateforEditRenewal()) {
                setIsNoCostEMI(false);
            }

            const input = {
                url: `coremrs/api/HealthRenewal/FetchHealthRenewalNeedAnalysis/` + RenewalLead,
                method: 'GET', service: 'MatrixCoreAPI'
            };
            CALL_API(input).then((response) => {
                response = response.Data;
                if (response) {
                    setNewHealthRenewalInfo({
                        NoCostEMI: response?.NoCostEMI,
                        PaymentMode: response.PaymentMode,
                        GSTRefundEligible: response.AllowedGSTRefund,
                        MigrationPlanId: response.MigrationPlanId,
                        CareMigrationPlanId: response.CareMigrationPlanId,
                        IsPlanMigrationReq: response.IsPlanMigrationReq,
                        IsCarePlanMigrationReq: response.IsCarePlanMigrationReq,
                        AllowedDiscount: response.AllowedDiscount
                    });
                    setOriginalInfo({
                        NoCostEMI: response?.NoCostEMI,
                        PaymentMode: response.PaymentMode,
                        GSTRefundEligible: response.AllowedGSTRefund,
                        MigrationPlanId: response.MigrationPlanId,
                        CareMigrationPlanId: response.CareMigrationPlanId,
                        IsPlanMigrationReq: response.IsPlanMigrationReq,
                        IsCarePlanMigrationReq: response.IsCarePlanMigrationReq,
                        AllowedDiscount: response.AllowedDiscount
                    });

                    //Care Plans Migration Eligibility
                    if ([38312, 39104, 79189, 101884].indexOf(response.OldPlanID) > 0 && User.RoleId !== 13) {
                        setCarePlanMigrationEligible(true);
                        if (response.PlanID === 38312 || response.PlanID === 39104) {
                            setCarePlans(CareSupremePlans);
                        }
                        if (response.PlanID === 79189 || response.PlanID === 101884) {
                            setCarePlans(CareSupremeValuePlans);
                        }
                    }

                    // No Cost EMI
                    if (response.NoCostEMI == 1)
                        setAlignment("right")
                    else if (response.NoCostEMI == 2)
                        setAlignment("left")
                    else setAlignment("center")

                    //No Cost EMI validation
                    if (IsRenewal && response.NoCostEMI === 1) {
                        setIsNoCostEMI(true);
                    }

                    // Niva Allowed Discount Eligibility on basis of Plan ID
                    if ([77295, 77224, 77293, 77294, 78001, 46548, 77997, 44427, 77980].indexOf(response.OldPlanID) > 0 && User.RoleId !== 13) {
                        setAllowedDiscountEligible(true); // 18 for niva
                    }
                }
                else {
                    setNewHealthRenewalInfo(HealthRenewalInfo);
                    setOriginalInfo(HealthRenewalInfo);
                }
            }, function () {
                {
                    setNewHealthRenewalInfo(HealthRenewalInfo);
                    setOriginalInfo(HealthRenewalInfo);
                }
            });
        }
        else {
            setNewHealthRenewalInfo(HealthRenewalInfo);
            setOriginalInfo(HealthRenewalInfo);
        }
    };
    const UpdateHealthRenewalInfo = () => {
        if (NewHealthRenewalInfo.IsPlanMigrationReq == true && NewHealthRenewalInfo.MigrationPlanId == 0) {
            enqueueSnackbar('Select the plan for migration.', {
                variant: 'error',
                autoHideDuration: 3000,
            });
        }
        else if (NewHealthRenewalInfo.IsCarePlanMigrationReq == true && NewHealthRenewalInfo.CareMigrationPlanId == 0) {
            enqueueSnackbar('Select the plan for migration.', {
                variant: 'error',
                autoHideDuration: 3000,
            });
        }
        else {
            let reqData = {
                LeadID: RenewalLead,
            }
            if (NewHealthRenewalInfo.NoCostEMI != originalInfo.NoCostEMI) {
                reqData.NoCostEMI = NewHealthRenewalInfo.NoCostEMI - 1
            }
            if (NewHealthRenewalInfo.PaymentMode != originalInfo.PaymentMode) {
                reqData.PaymentMode = NewHealthRenewalInfo.PaymentMode
            }
            if (NewHealthRenewalInfo.AllowedDiscount != originalInfo.AllowedDiscount) {
                reqData.AllowedDiscount = NewHealthRenewalInfo.AllowedDiscount
                reqData.IsDiscountReq = (NewHealthRenewalInfo.AllowedDiscount != originalInfo.AllowedDiscount) || (NewHealthRenewalInfo.AllowedDiscount == true) ? true : false
            }
            if (NewHealthRenewalInfo.GSTRefundEligible != originalInfo.GSTRefundEligible) {
                reqData.IsGSTUpdateReq = IsGSTRefundEligible
                reqData.AllowedGSTRefund = NewHealthRenewalInfo.GSTRefundEligible
            }
            if (NewHealthRenewalInfo.IsPlanMigrationReq != originalInfo.IsPlanMigrationReq || NewHealthRenewalInfo.MigrationPlanId != originalInfo.MigrationPlanId) {
                reqData.IsPlanMigrationReq = NewHealthRenewalInfo.IsPlanMigrationReq,
                    reqData.MigrationPlanId = NewHealthRenewalInfo.MigrationPlanId
            }
            if (NewHealthRenewalInfo.IsCarePlanMigrationReq != originalInfo.IsCarePlanMigrationReq || NewHealthRenewalInfo.CareMigrationPlanId != originalInfo.CareMigrationPlanId) {
                reqData.IsCarePlanMigrationReq = NewHealthRenewalInfo.IsCarePlanMigrationReq
                reqData.CareMigrationPlanId = NewHealthRenewalInfo.CareMigrationPlanId
            }
            if (NewHealthRenewalInfo != originalInfo) {
                setHealthRenewalNeedAnalysisService(reqData).then(function (resultData) {
                    if (resultData) {
                        SetLeadAudits(NewHealthRenewalInfo, originalInfo);
                        getHealthRenewalInfo();
                        SaveComment("Customer Additional info Updated.");
                        enqueueSnackbar("Details Updated Successfully.", {
                            variant: 'success',
                            autoHideDuration: 3000,
                        });
                    }
                }, function () {
                    enqueueSnackbar('Something went wrong, Please Connect the Support team.', {
                        variant: 'error',
                        autoHideDuration: 3000,
                    });
                });
            }
            else {
                enqueueSnackbar("No Change in Details.", {
                    variant: 'success',
                    autoHideDuration: 3000,
                });
            }
        }
    }

    const handleToggle = (e) => {
        setShow(!show);
    }
    useEffect(() => {
        if (RefreshLead) {
            setShow(false);
        }
    }, [RefreshLead]);

    useEffect(() => {
        if (show == true) {
            setRenewalLead(visibleLeads[0]);
            checkLeadEligibleFields(visibleLeads[0]);
            getHealthRenewalInfo();
            ShowMonthlymode();
        }
    }, [show]);

    return <>
        <div className="addInfo">
            <h3>Renewal Additional Info</h3>
            <div className="expandmoreIcon">
                <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
            <p className="caption">Renewal specific additional information </p>
            {show &&
                <>
                    <SelectDropdown
                        name="RenewalLead"
                        label="Renewal Lead"
                        value={RenewalLead}
                        options={visibleLeads}
                        labelKeyInOptions='_all'
                        valueKeyInOptions="_all"
                        handleChange={handleChange}
                        sm={12} md={12} xs={12}
                    />
                    <div className="Additional-details">
                        <div>No Cost EMI</div>
                        <ToggleButtonGroup
                            value={alignment}
                            exclusive
                            onChange={handleAlignment}
                            aria-label="text alignment"
                            className="NoCostEMIToggle"
                            name="NoCostEMI"
                        >
                            <ToggleButton value="left" aria-label="left aligned" disabled={IsNoCostEMI} >
                                Yes
                            </ToggleButton>
                            <ToggleButton value="center" aria-label="centered" disabled={alignment != "center"}>
                                None
                            </ToggleButton>
                            <ToggleButton value="right" aria-label="right aligned" disabled={IsNoCostEMI}>
                                No
                            </ToggleButton>
                        </ToggleButtonGroup>
                    </div>
                    {showPaymentMode && IsMonthlyMode && <>
                        <div className="Additional-details">
                            <div>Payment Mode</div>
                        </div>

                        <SelectDropdown
                            name="PaymentMode"
                            value={NewHealthRenewalInfo.PaymentMode}
                            options={FilterDropdown}
                            labelKeyInOptions='name'
                            valueKeyInOptions="id"
                            handleChange={handleChange}
                            sm={12} md={12} xs={12}
                        />
                    </>}
                    {IsGSTRefundEligible && ShowGST && <>
                        <div className="Additional-details">
                            <div>GSTRefundEligible</div>
                            <div>
                                {NewHealthRenewalInfo.GSTRefundEligible ? "Yes" : "No"}
                                <Switch
                                    checked={!!NewHealthRenewalInfo.GSTRefundEligible}
                                    onChange={handleChange}
                                    name="GSTRefundEligible"
                                />
                            </div>
                        </div>
                    </>}
                    {AllowedDiscountEligible && <>
                        <div className="Additional-details">
                            <div>Allowed Discount</div>
                            <div>
                                {NewHealthRenewalInfo.AllowedDiscount ? "Yes" : "No"}
                                <Switch
                                    checked={!!NewHealthRenewalInfo.AllowedDiscount}
                                    onChange={handleChange}
                                    name="AllowedDiscount"
                                />
                            </div>
                        </div>
                    </>}
                    {PlanMigrationEligible && <>
                        <div className="Additional-details">
                            <div>Plan Migration Requested</div>
                            <div>
                                {NewHealthRenewalInfo.IsPlanMigrationReq ? "Yes" : "No"}
                                <Switch
                                    checked={!!NewHealthRenewalInfo.IsPlanMigrationReq}
                                    onChange={handleChange}
                                    name="IsPlanMigrationReq"
                                />
                            </div>
                        </div>
                        {NewHealthRenewalInfo.IsPlanMigrationReq == true &&
                            <>
                                <div className="Additional-details">
                                    <div>Plan Requested</div>
                                </div>

                                <SelectDropdown
                                    name="MigrationPlanId"
                                    label="Migration Plan"
                                    value={NewHealthRenewalInfo.MigrationPlanId}
                                    options={Plans}
                                    labelKeyInOptions='Name'
                                    valueKeyInOptions="Id"
                                    handleChange={handleChange}
                                    sm={12} md={12} xs={12}
                                />
                            </>
                        }
                    </>}
                    {CarePlanMigrationEligible && <>
                        <div className="Additional-details">
                            <div>Care Plan Migration Requested</div>
                            <div>
                                {NewHealthRenewalInfo.IsCarePlanMigrationReq ? "Yes" : "No"}
                                <Switch
                                    checked={!!NewHealthRenewalInfo.IsCarePlanMigrationReq}
                                    onChange={handleChange}
                                    name="IsCarePlanMigrationReq"
                                />
                            </div>
                        </div>
                        {NewHealthRenewalInfo.IsCarePlanMigrationReq == true &&
                            <>
                                <div className="Additional-details">
                                    <div>Plan Requested</div>
                                </div>

                                <SelectDropdown
                                    name="CareMigrationPlanId"
                                    label="Migration Plan"
                                    value={NewHealthRenewalInfo.CareMigrationPlanId}
                                    options={CarePlans}
                                    labelKeyInOptions='Name'
                                    valueKeyInOptions="Id"
                                    handleChange={handleChange}
                                    sm={12} md={12} xs={12}
                                />
                            </>
                        }
                    </>}
                    <button onClick={UpdateHealthRenewalInfo} className="submitBtn">Submit</button>
                </>
            }
        </div>
    </>

}
export default RenewalAddInfo;

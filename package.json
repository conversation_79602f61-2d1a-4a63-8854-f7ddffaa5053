{"name": "salesview", "version": "2.0.0", "description": "Matrix web application", "main": "index.js", "scripts": {"start": "webpack-dev-server --port 61609 --open --mode development --config webpack.dev.js", "startHttps": "webpack-dev-server --https --port 3234 --host svlocalhost.policybazaar.com --open --mode development --config webpack.dev.js", "build": "webpack --mode production && cp -r softphone dist/", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": [], "author": "Matrix", "license": "ISC", "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@date-io/date-fns": "^1.3.11", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.13", "@mui/lab": "^5.0.0-alpha.148", "@mui/material": "^5.14.13", "@mui/styles": "^5.14.13", "@mui/x-date-pickers": "^6.16.1", "@react-google-maps/api": "2.19.2", "clsx": "^1.0.4", "copy-webpack-plugin": "^11.0.0", "date-fns": "^2.6.0", "dayjs": "^1.10.2", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "history": "^4.9.0", "lodash": "^4.17.21", "notistack": "^3.0.1", "prop-types": "^15.7.2", "react": "^17.0.0", "react-csv": "^2.2.2", "react-data-table-component": "^7.4.5", "react-dom": "^17.0.0", "react-dropzone": "^11.3.4", "react-horizontal-scrolling-menu": "^0.7.8", "react-perfect-scrollbar": "^1.5.3", "react-redux": "^7.2.2", "react-router-dom": "^5.0.1", "react-slick": "^0.27.14", "redux-persist": "5.10.0", "sass": "^1.68.0", "styled-components": "^5.3.3", "to-words": "^4.0.0", "underscore": "^1.11.0", "use-places-autocomplete": "^4.0.1", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "7.22.8", "@babel/plugin-transform-runtime": "7.4.3", "@babel/preset-env": "7.22.20", "@babel/preset-react": "7.0.0", "acorn": "^6.4.2", "autoprefixer": "^9.6.4", "axios": "^1.5.0", "babel-loader": "8.0.5", "babel-plugin-emotion": "^10.0.23", "babel-plugin-transform-class-properties": "^6.24.1", "clean-webpack-plugin": "^4.0.0", "css-loader": "^6.8.1", "dotenv": "^8.1.0", "eslint": "8.49.0", "eslint-config-react-app": "^7.0.1", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "mini-css-extract-plugin": "^2.7.6", "postcss-loader": "^7.3.3", "redux": "4.0.1", "redux-thunk": "2.3.0", "sass": "^1.68.0", "sass-loader": "13.3.2", "style-loader": "0.23.1", "url-loader": "^4.1.1", "webpack": "5.88.2", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.15.1"}}